<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gerador PIX QR Code Personalizado</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family:
          "Inter",
          -apple-system,
          BlinkMacSystemFont,
          "Segoe UI",
          Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1600px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.1em;
        opacity: 0.9;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 0;
      }

      .form-container {
        padding: 30px;
        background: #f8f9fa;
        border-right: 1px solid #e9ecef;
        max-height: 800px;
        overflow-y: auto;
      }

      .result-container {
        padding: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: white;
      }

      .form-header {
        margin-bottom: 30px;
      }

      .form-header h2 {
        color: #495057;
        font-size: 1.8em;
        margin-bottom: 8px;
      }

      .form-header p {
        color: #6c757d;
        font-size: 1.1em;
      }

      .pix-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group.full-width {
        grid-column: 1 / -1;
      }

      .form-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        font-size: 0.9em;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        font-family: inherit;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .char-counter,
      .form-group small {
        font-size: 0.8em;
        color: #6c757d;
        margin-top: 4px;
      }

      .validation-message {
        font-size: 0.8em;
        margin-top: 4px;
      }

      .validation-message.success {
        color: #28a745;
      }

      .validation-message.error {
        color: #dc3545;
      }

      /* Toggle Switch */
      .customization-toggle {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 30px 0 20px 0;
        padding: 20px;
        background: white;
        border-radius: 12px;
        border: 2px solid #e9ecef;
      }

      .toggle-switch {
        position: relative;
        width: 50px;
        height: 24px;
        background: #ccc;
        border-radius: 12px;
        cursor: pointer;
        transition: background 0.3s;
      }

      .toggle-switch.active {
        background: #667eea;
      }

      .toggle-slider {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s;
      }

      .toggle-switch.active .toggle-slider {
        transform: translateX(26px);
      }

      .toggle-label {
        font-weight: 600;
        color: #495057;
      }

      .toggle-description {
        font-size: 0.9em;
        color: #6c757d;
      }

      /* Customization Panel */
      .customization-panel {
        display: none;
        background: white;
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 20px;
        margin-top: 20px;
      }

      .customization-panel.active {
        display: block;
      }

      .section-title {
        font-size: 1.1em;
        font-weight: 700;
        color: #495057;
        margin: 20px 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #e9ecef;
      }

      .section-title:first-child {
        margin-top: 0;
      }

      .color-input {
        height: 45px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
      }

      .form-control-small {
        padding: 8px;
        font-size: 13px;
      }

      /* Buttons */
      .generate-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-top: 30px;
      }

      .generate-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      .generate-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Result Area */
      .qr-placeholder {
        text-align: center;
        color: #6c757d;
        padding: 60px 20px;
      }

      .qr-placeholder .qr-icon {
        margin-bottom: 20px;
      }

      .qr-result {
        text-align: center;
        display: none;
      }

      .qr-image-container {
        position: relative;
        margin-bottom: 20px;
      }

      #qr-preview {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 10px;
        background: white;
        min-height: 300px;
        min-width: 300px;
        max-height: 400px;
        max-width: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      #qr-preview.margin-none {
        padding: 1px;
        border: 2px solid #e9ecef; /* Neutral border */
        overflow: hidden; /* Ensure cropping works properly */
      }

      #qr-preview.margin-none canvas,
      #qr-preview.margin-none svg {
        /* Optimize rendering for zero-margin QR codes */
        image-rendering: -moz-crisp-edges;
        image-rendering: -webkit-crisp-edges;
        image-rendering: pixelated;
        image-rendering: crisp-edges;

        /* Ensure pixel-perfect alignment */
        transform-origin: center center;

        /* Prevent blurry rendering on high-DPI displays */
        -webkit-font-smoothing: none;
        -moz-osx-font-smoothing: grayscale;
      }

      #qr-preview.margin-default {
        padding: 10px;
        border: 2px solid #e9ecef; /* Neutral border */
      }

      #qr-preview.margin-wide {
        padding: 20px;
        border: 2px solid #e9ecef; /* Neutral border */
      }

      #qrImage {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        border-radius: 8px;
        object-fit: contain;
      }

      .download-btn,
      .copy-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin: 5px;
        transition: all 0.3s ease;
      }

      .download-btn:hover,
      .copy-btn:hover {
        background: #218838;
        transform: translateY(-1px);
      }

      .qr-info h3 {
        color: #28a745;
        margin-bottom: 15px;
      }

      .br-code-container {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .br-code-container label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
      }

      .br-code-text {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        font-family: monospace;
        font-size: 0.9em;
        word-break: break-all;
        margin-bottom: 10px;
      }

      .pix-details {
        margin-top: 20px;
      }

      .pix-detail-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
      }

      .pix-detail-label {
        font-weight: 600;
        color: #495057;
      }

      .pix-detail-value {
        color: #6c757d;
        text-align: right;
      }

      /* Modal */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 0;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      .modal-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 20px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .modal-header h3 {
        margin: 0;
      }

      .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .modal-body {
        padding: 20px;
      }

      /* Toast */
      .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 600;
        z-index: 1001;
        transform: translateX(100%);
        transition: transform 0.3s ease;
      }

      .toast.show {
        transform: translateX(0);
      }

      .toast.error {
        background: #dc3545;
      }

      /* Image upload */
      .image-upload {
        position: relative;
      }

      .image-upload input[type="file"] {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      .image-upload-label {
        display: block;
        padding: 12px;
        border: 2px dashed #e9ecef;
        border-radius: 8px;
        text-align: center;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .image-upload-label:hover {
        border-color: #667eea;
        color: #667eea;
      }

      /* Preset buttons */
      .preset-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 15px;
      }

      .preset-btn {
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: white;
        color: #495057;
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
      }

      .preset-btn:hover {
        border-color: #667eea;
        color: #667eea;
        background: #f8f9ff;
      }

      /* Range inputs */
      input[type="range"] {
        width: 100%;
        margin: 10px 0;
      }

      .range-value {
        font-size: 0.9em;
        color: #6c757d;
        text-align: center;
      }

      /* Margin button group */
      .margin-button-group {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 8px;
        margin-top: 8px;
      }

      .margin-btn {
        padding: 10px 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background: white;
        color: #495057;
        font-size: 0.9em;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
      }

      .margin-btn:hover {
        border-color: #667eea;
        color: #667eea;
        background: #f8f9ff;
      }

      .margin-btn.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .margin-btn .margin-label {
        font-weight: 600;
        font-size: 0.85em;
      }

      .margin-btn .margin-value {
        font-size: 0.75em;
        opacity: 0.8;
      }

      /* Responsive */
      @media (max-width: 1024px) {
        .main-content {
          grid-template-columns: 1fr;
        }

        .form-container {
          max-height: none;
          border-right: none;
          border-bottom: 1px solid #e9ecef;
        }

        #qr-preview {
          max-width: 350px;
          max-height: 350px;
        }
      }

      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 15px;
        }

        .header {
          padding: 20px;
        }

        .header h1 {
          font-size: 2em;
        }

        .form-container,
        .result-container {
          padding: 20px;
        }

        .form-row {
          grid-template-columns: 1fr;
          gap: 15px;
        }

        #qr-preview {
          max-width: 300px;
          max-height: 300px;
          min-width: 250px;
          min-height: 250px;
        }
      }

      @media (max-width: 480px) {
        #qr-preview {
          max-width: 250px;
          max-height: 250px;
          min-width: 200px;
          min-height: 200px;
          padding: 10px;
        }

        #qr-preview.margin-none {
          padding: 3px;
        }

        #qr-preview.margin-default {
          padding: 8px;
        }

        #qr-preview.margin-wide {
          padding: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1>🎨 Gerador PIX QR Code Personalizado</h1>
        <p>Crie QR Codes PIX com estilo profissional e personalizações avançadas</p>
      </header>

      <main class="main-content">
        <div class="form-container">
          <div class="form-header">
            <h2>Dados PIX</h2>
            <p>Preencha os dados para gerar seu QR Code PIX</p>
          </div>

          <form id="pixForm" class="pix-form">
            <div class="form-row">
              <div class="form-group">
                <label for="keyType">Tipo de Chave *</label>
                <select id="keyType" name="keyType" required>
                  <option value="cpf">CPF</option>
                  <option value="phone">Telefone</option>
                  <option value="email">Email</option>
                  <option value="random">Chave Aleatória</option>
                </select>
              </div>

              <div class="form-group">
                <label for="pixKey">Chave PIX *</label>
                <input type="text" id="pixKey" name="pixKey" placeholder="000.000.000-00" required />
                <div class="validation-message" id="keyValidation"></div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="receiverName">Nome *</label>
                <input
                  type="text"
                  id="receiverName"
                  name="receiverName"
                  maxlength="25"
                  placeholder="Nome do recebedor"
                  required
                />
                <small class="char-counter">0/25 caracteres</small>
              </div>

              <div class="form-group">
                <label for="receiverCity">Cidade *</label>
                <input
                  type="text"
                  id="receiverCity"
                  name="receiverCity"
                  maxlength="15"
                  placeholder="Cidade do recebedor"
                  required
                />
                <small class="char-counter">0/15 caracteres</small>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="amount">Valor (opcional)</label>
                <input type="text" id="amount" name="amount" placeholder="R$ 0,00" class="currency-input" />
                <small>Deixe em branco para valor livre</small>
              </div>

              <div class="form-group">
                <label for="reference">Referência (opcional)</label>
                <input type="text" id="reference" name="reference" placeholder="Ex: COMPRA123" maxlength="25" />
                <small>Use apenas letras e números, sem espaços ou caracteres especiais.</small>
              </div>
            </div>

            <div class="form-group full-width">
              <label for="description">Descrição (opcional)</label>
              <input
                type="text"
                id="description"
                name="description"
                placeholder="Descrição da transação"
                maxlength="50"
              />
            </div>

            <!-- Toggle para personalização -->
            <div class="customization-toggle">
              <div class="toggle-switch" id="customizationToggle">
                <div class="toggle-slider"></div>
              </div>
              <div>
                <div class="toggle-label">Personalizar QR Code</div>
                <div class="toggle-description">Ative para acessar opções avançadas de estilo</div>
              </div>
            </div>

            <!-- Painel de personalização -->
            <div class="customization-panel" id="customizationPanel">
              <div class="section-title">🎨 Estilo dos Pontos</div>

              <div class="form-row">
                <div class="form-group">
                  <label for="dots-type">Tipo dos Pontos</label>
                  <select id="dots-type" class="form-control">
                    <option value="square">Quadrado</option>
                    <option value="rounded" selected>Arredondado</option>
                    <option value="dots">Círculo</option>
                    <option value="classy">Clássico</option>
                    <option value="classy-rounded">Clássico Arredondado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="dots-color">Cor dos Pontos</label>
                  <input type="color" id="dots-color" class="color-input" value="#000000" />
                </div>
              </div>

              <div class="section-title">📐 Position Patterns (Cantos)</div>

              <div class="form-row">
                <div class="form-group">
                  <label for="corner-square-type">Tipo dos Quadrados</label>
                  <select id="corner-square-type" class="form-control-small">
                    <option value="">Padrão</option>
                    <option value="square">Quadrado</option>
                    <option value="rounded">Arredondado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                    <option value="dot">Círculo</option>
                    <option value="classy">Clássico</option>
                    <option value="classy-rounded">Clássico Arredondado</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="corner-square-color">Cor dos Quadrados</label>
                  <input type="color" id="corner-square-color" class="color-input form-control-small" value="#000000" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="corner-dot-type">Tipo dos Pontos Centrais</label>
                  <select id="corner-dot-type" class="form-control-small">
                    <option value="">Padrão</option>
                    <option value="square">Quadrado</option>
                    <option value="dot">Círculo</option>
                    <option value="classy">Clássico</option>
                    <option value="classy-rounded">Clássico Arredondado</option>
                    <option value="extra-rounded">Extra Arredondado</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="corner-dot-color">Cor dos Pontos Centrais</label>
                  <input type="color" id="corner-dot-color" class="color-input form-control-small" value="#000000" />
                </div>
              </div>

              <div class="section-title">🖼️ Fundo</div>

              <div class="form-group">
                <label for="background-color">Cor de Fundo</label>
                <input type="color" id="background-color" class="color-input" value="#ffffff" />
              </div>

              <div class="section-title">🏷️ Imagem Central</div>

              <div class="form-group">
                <div class="image-upload">
                  <input type="file" id="center-image" accept="image/*" />
                  <label for="center-image" class="image-upload-label">
                    📁 Clique para selecionar uma imagem<br />
                    <small>(PNG, JPG, SVG)</small>
                  </label>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="image-size">Tamanho da Imagem</label>
                  <input type="range" id="image-size" min="0.1" max="0.8" step="0.1" value="0.4" />
                  <div class="range-value" id="image-size-value">40%</div>
                </div>
                <div class="form-group">
                  <label for="image-margin">Margem da Imagem</label>
                  <input type="number" id="image-margin" class="form-control-small" value="0" min="0" max="50" />
                </div>
              </div>

              <div class="form-group">
                <label>
                  <input type="checkbox" id="hide-background-dots" checked />
                  Ocultar pontos sob a imagem
                </label>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="qr-width">Largura (px)</label>
                  <input type="number" id="qr-width" class="form-control-small" value="300" min="200" max="1000" />
                </div>
                <div class="form-group">
                  <label for="qr-height">Altura (px)</label>
                  <input type="number" id="qr-height" class="form-control-small" value="300" min="200" max="1000" />
                </div>
              </div>
              <div class="form-group">
                <small style="color: #6c757d; font-style: italic; display: block; margin-top: 5px">
                  ℹ️ Dimensões entre 200px e 1000px. Valores aplicados no download; pré-visualização limitada a
                  400x400px.
                </small>
              </div>

              <div class="form-group">
                <label>Margem (px)</label>
                <div class="margin-button-group">
                  <button type="button" class="margin-btn" data-margin="0">
                    <span class="margin-label">Nenhuma</span>
                    <span class="margin-value">0px</span>
                  </button>
                  <button type="button" class="margin-btn active" data-margin="10">
                    <span class="margin-label">Padrão</span>
                    <span class="margin-value">10px</span>
                  </button>
                  <button type="button" class="margin-btn" data-margin="30">
                    <span class="margin-label">Ampla</span>
                    <span class="margin-value">30px</span>
                  </button>
                </div>
              </div>

              <div class="section-title">🎁 Presets</div>
              <div class="preset-buttons">
                <button type="button" class="preset-btn" onclick="applyPreset('modern')">🎨 Moderno</button>
                <button type="button" class="preset-btn" onclick="applyPreset('classic')">📱 Clássico</button>
                <button type="button" class="preset-btn" onclick="applyPreset('elegant')">✨ Elegante</button>
                <button type="button" class="preset-btn" onclick="applyPreset('vibrant')">🌈 Vibrante</button>
                <button
                  type="button"
                  class="preset-btn"
                  onclick="applyPreset('circular')"
                  style="background: linear-gradient(135deg, #e74c3c, #3498db); color: white"
                >
                  Circular
                </button>
              </div>
            </div>

            <button type="submit" class="generate-btn" id="generateBtn">
              <span class="btn-text">Gerar QR Code PIX</span>
              <div class="loading-spinner" id="loadingSpinner"></div>
            </button>
          </form>
        </div>

        <!-- Área de Resultado -->
        <div class="result-container">
          <div class="qr-placeholder" id="qrPlaceholder">
            <div class="qr-icon">
              <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                <!-- QR Code placeholder icon -->
                <rect x="10" y="10" width="25" height="25" fill="#6b7280" rx="4" />
                <rect x="85" y="10" width="25" height="25" fill="#6b7280" rx="4" />
                <rect x="10" y="85" width="25" height="25" fill="#6b7280" rx="4" />

                <rect x="15" y="15" width="15" height="15" fill="white" rx="2" />
                <rect x="90" y="15" width="15" height="15" fill="white" rx="2" />
                <rect x="15" y="90" width="15" height="15" fill="white" rx="2" />

                <rect x="19" y="19" width="7" height="7" fill="#6b7280" />
                <rect x="94" y="19" width="7" height="7" fill="#6b7280" />
                <rect x="19" y="94" width="7" height="7" fill="#6b7280" />

                <!-- Pattern dots -->
                <rect x="45" y="45" width="5" height="5" fill="#6b7280" />
                <rect x="55" y="45" width="5" height="5" fill="#6b7280" />
                <rect x="65" y="45" width="5" height="5" fill="#6b7280" />
                <rect x="75" y="45" width="5" height="5" fill="#6b7280" />

                <rect x="45" y="55" width="5" height="5" fill="#6b7280" />
                <rect x="65" y="55" width="5" height="5" fill="#6b7280" />

                <rect x="45" y="65" width="5" height="5" fill="#6b7280" />
                <rect x="55" y="65" width="5" height="5" fill="#6b7280" />
                <rect x="65" y="65" width="5" height="5" fill="#6b7280" />
                <rect x="75" y="65" width="5" height="5" fill="#6b7280" />
              </svg>
            </div>
            <h3>Nenhum QR Code Gerado</h3>
            <p>Utilize o formulário ao lado para gerar seu QR Code PIX personalizado.</p>
          </div>

          <div class="qr-result" id="qrResult" style="display: none">
            <div class="qr-image-container">
              <div id="qr-preview">
                <div style="color: #6c757d">O QR Code aparecerá aqui</div>
              </div>
              <button class="download-btn" id="downloadBtn">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M8 1V11M8 11L11 8M8 11L5 8M2 13H14"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Download PNG
              </button>
              <button class="download-btn" id="downloadSvgBtn" style="background: #6f42c1">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M8 1V11M8 11L11 8M8 11L5 8M2 13H14"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Download SVG
              </button>
            </div>

            <div class="qr-info">
              <h3>QR Code PIX Gerado com Sucesso!</h3>
              <div class="br-code-container">
                <label>BR Code:</label>
                <div class="br-code-text" id="brCodeText"></div>
                <button class="copy-btn" id="copyBtn">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path
                      d="M4 4V2C4 1.44772 4.44772 1 5 1H13C13.5523 1 14 1.44772 14 2V10C14 10.5523 13.5523 11 13 11H11M10 5H3C2.44772 5 2 5.44772 2 6V13C2 13.5523 2.44772 14 3 14H10C10.5523 14 11 13.5523 11 13V6C11 5.44772 10.5523 5 10 5Z"
                      stroke="currentColor"
                      stroke-width="1.5"
                    />
                  </svg>
                  Copiar
                </button>
              </div>

              <div class="pix-details" id="pixDetails"></div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Erro</h3>
          <button class="close-btn" id="closeModal">&times;</button>
        </div>
        <div class="modal-body">
          <p id="errorMessage"></p>
        </div>
      </div>
    </div>

    <script src="../lib/qr-code-styling.js"></script>
    <script>
      // PIX QR Code Generator with Advanced Customization
      // ATUALIZAÇÃO: Implementada padronização visual para consistência entre QR Codes
      // - Sempre usa qr-code-styling quando disponível
      // - Configurações padronizadas para QR Codes sem personalização
      // - Nível de correção de erro padronizado (M) para consistência visual
      class PixQRGeneratorIntegrated {
        constructor() {
          this.initializeElements();
          this.setupEventListeners();
          this.setupValidation();
          this.setupMasks();
          this.setupCustomization();
          this.currentBRCode = "";
          this.qrCode = null;
          this.currentImageFile = null;
          this.exportOptions = null;
        }

        initializeElements() {
          // Form elements
          this.form = document.getElementById("pixForm");
          this.keyTypeSelect = document.getElementById("keyType");
          this.pixKeyInput = document.getElementById("pixKey");
          this.receiverNameInput = document.getElementById("receiverName");
          this.receiverCityInput = document.getElementById("receiverCity");
          this.amountInput = document.getElementById("amount");
          this.referenceInput = document.getElementById("reference");
          this.descriptionInput = document.getElementById("description");

          // UI elements
          this.generateBtn = document.getElementById("generateBtn");
          this.loadingSpinner = document.getElementById("loadingSpinner");
          this.qrPlaceholder = document.getElementById("qrPlaceholder");
          this.qrResult = document.getElementById("qrResult");
          this.qrPreview = document.getElementById("qr-preview");
          this.brCodeText = document.getElementById("brCodeText");
          this.pixDetails = document.getElementById("pixDetails");
          this.downloadBtn = document.getElementById("downloadBtn");
          this.downloadSvgBtn = document.getElementById("downloadSvgBtn");
          this.copyBtn = document.getElementById("copyBtn");

          // Modal elements
          this.errorModal = document.getElementById("errorModal");
          this.errorMessage = document.getElementById("errorMessage");
          this.closeModal = document.getElementById("closeModal");

          // Validation element
          this.keyValidation = document.getElementById("keyValidation");

          // Customization elements
          this.customizationToggle = document.getElementById("customizationToggle");
          this.customizationPanel = document.getElementById("customizationPanel");
        }

        setupCustomization() {
          // Toggle switch
          this.customizationToggle.addEventListener("click", () => {
            this.customizationToggle.classList.toggle("active");
            this.customizationPanel.classList.toggle("active");
          });

          // Customization controls event listeners
          const customizationInputs = [
            "dots-type",
            "dots-color",
            "corner-square-type",
            "corner-square-color",
            "corner-dot-type",
            "corner-dot-color",
            "background-color",
            "qr-width",
            "qr-height",
            "image-size",
            "image-margin",
            "hide-background-dots"
          ];

          customizationInputs.forEach((id) => {
            const element = document.getElementById(id);
            if (element) {
              if (element.type === "range") {
                element.addEventListener("input", () => this.updateRangeValue(id));
              }

              // Add validation for width and height inputs
              if (id === "qr-width" || id === "qr-height") {
                element.addEventListener("input", (e) => this.validateDimensionInput(e));
                element.addEventListener("change", (e) => this.validateDimensionInput(e));
              }

              element.addEventListener("change", () => this.generateQRIfReady());
              element.addEventListener("input", () => this.generateQRIfReady());
            }
          });

          // Setup margin button group
          this.setupMarginButtons();

          // Initialize margin container class
          this.updatePreviewContainerMargin();

          // Image upload
          const centerImage = document.getElementById("center-image");
          if (centerImage) {
            centerImage.addEventListener("change", (e) => this.handleImageUpload(e));
          }

          // Initialize range values
          this.updateRangeValue("image-size");
        }

        setupMarginButtons() {
          const marginButtons = document.querySelectorAll(".margin-btn");
          marginButtons.forEach((button) => {
            button.addEventListener("click", (e) => {
              e.preventDefault();

              // Remove active class from all buttons
              marginButtons.forEach((btn) => btn.classList.remove("active"));

              // Add active class to clicked button
              button.classList.add("active");

              // Update container padding based on margin selection
              this.updatePreviewContainerMargin();

              // Regenerate QR code with new margin
              this.generateQRIfReady();
            });
          });
        }

        updatePreviewContainerMargin() {
          const qrPreview = this.qrPreview;
          const currentMargin = this.getCurrentMargin();

          console.log("updatePreviewContainerMargin - current margin:", currentMargin);
          console.log("qrPreview element:", qrPreview);

          // Remove existing margin classes
          qrPreview.classList.remove("margin-none", "margin-default", "margin-wide");

          // Add appropriate margin class
          switch (currentMargin) {
            case 0:
              qrPreview.classList.add("margin-none");
              console.log("Added margin-none class");
              break;
            case 10:
              qrPreview.classList.add("margin-default");
              console.log("Added margin-default class");
              break;
            case 30:
              qrPreview.classList.add("margin-wide");
              console.log("Added margin-wide class");
              break;
          }

          console.log("Final classList:", qrPreview.classList.toString());
        }

        getCurrentMargin() {
          const activeMarginBtn = document.querySelector(".margin-btn.active");
          const margin = activeMarginBtn ? parseInt(activeMarginBtn.dataset.margin) : 10;

          // Handle margin 0 issue - some QR libraries have minimum margin requirements
          if (margin === 0) {
            console.log("Using margin 0 with forced settings");
            return 0; // Keep 0, but we'll handle it differently
          }

          console.log("getCurrentMargin returning:", margin);
          return margin;
        }

        updateRangeValue(inputId) {
          const input = document.getElementById(inputId);
          if (!input) return;

          let valueElement;
          let displayValue;

          switch (inputId) {
            case "image-size":
              valueElement = document.getElementById("image-size-value");
              displayValue = Math.round(input.value * 100) + "%";
              break;
          }

          if (valueElement) {
            valueElement.textContent = displayValue;
          }
        }

        validateDimensionInput(event) {
          const input = event.target;
          const value = parseInt(input.value);
          const minSize = 200;
          const maxSize = 1000;

          // Validate and correct the value
          if (isNaN(value) || value < minSize) {
            input.value = minSize;
            this.showToast(`Tamanho mínimo é ${minSize}px`, "error");
          } else if (value > maxSize) {
            input.value = maxSize;
            this.showToast(`Tamanho máximo é ${maxSize}px`, "error");
          }
        }

        handleImageUpload(event) {
          const file = event.target.files[0];
          if (file) {
            this.currentImageFile = file;
            const reader = new FileReader();
            reader.onload = () => {
              this.generateQRIfReady();
            };
            reader.readAsDataURL(file);

            // Update label
            const label = document.querySelector(".image-upload-label");
            if (label) {
              label.innerHTML = `✅ ${file.name}<br><small>Clique para alterar</small>`;
            }
          }
        }

        setupEventListeners() {
          // Form submission
          this.form.addEventListener("submit", (e) => this.handleFormSubmit(e));

          // Key type change
          this.keyTypeSelect.addEventListener("change", () => this.handleKeyTypeChange());

          // PIX key input validation
          this.pixKeyInput.addEventListener("input", () => this.validatePixKey());
          this.pixKeyInput.addEventListener("blur", () => this.validatePixKey());

          // Character counters
          this.receiverNameInput.addEventListener("input", () => this.updateCharCounter(this.receiverNameInput, 25));
          this.receiverCityInput.addEventListener("input", () => this.updateCharCounter(this.receiverCityInput, 15));

          // Buttons
          this.downloadBtn.addEventListener("click", () => this.downloadQRCode("png"));
          this.downloadSvgBtn.addEventListener("click", () => this.downloadQRCode("svg"));
          this.copyBtn.addEventListener("click", () => this.copyBRCode());
          this.closeModal.addEventListener("click", () => this.hideModal());

          // Modal close on backdrop click
          this.errorModal.addEventListener("click", (e) => {
            if (e.target === this.errorModal) {
              this.hideModal();
            }
          });

          // ESC key to close modal
          document.addEventListener("keydown", (e) => {
            if (e.key === "Escape" && this.errorModal.style.display === "block") {
              this.hideModal();
            }
          });
        }

        setupValidation() {
          // Initial key type setup
          this.handleKeyTypeChange();
        }

        setupMasks() {
          // Currency mask for amount input
          this.amountInput.addEventListener("input", (e) => {
            let value = e.target.value.replace(/\D/g, "");
            if (value.length === 0) {
              e.target.value = "";
              return;
            }

            value = (parseInt(value) / 100).toFixed(2);
            e.target.value = "R$ " + value.replace(".", ",");
          });

          // Reference input: only alphanumeric
          this.referenceInput.addEventListener("input", (e) => {
            e.target.value = e.target.value.replace(/[^A-Za-z0-9]/g, "");
          });
        }

        handleKeyTypeChange() {
          const keyType = this.keyTypeSelect.value;
          const pixKeyInput = this.pixKeyInput;

          // Clear previous value and validation
          pixKeyInput.value = "";
          this.keyValidation.textContent = "";
          this.keyValidation.className = "validation-message";

          // Update placeholder and input type based on key type
          switch (keyType) {
            case "cpf":
              pixKeyInput.placeholder = "000.000.000-00";
              pixKeyInput.type = "text";
              pixKeyInput.maxLength = 14;
              this.setupCPFMask();
              break;
            case "phone":
              pixKeyInput.placeholder = "(11) 99999-9999";
              pixKeyInput.type = "tel";
              pixKeyInput.maxLength = 15;
              this.setupPhoneMask();
              break;
            case "email":
              pixKeyInput.placeholder = "<EMAIL>";
              pixKeyInput.type = "email";
              pixKeyInput.maxLength = 50;
              this.removeMask();
              break;
            case "random":
              pixKeyInput.placeholder = "chave-aleatoria-uuid";
              pixKeyInput.type = "text";
              pixKeyInput.maxLength = 50;
              this.removeMask();
              break;
          }

          // Revalidate if there's already content
          if (pixKeyInput.value.trim()) {
            this.validatePixKey();
          }
        }

        setupCPFMask() {
          this.pixKeyInput.addEventListener(
            "input",
            (this.cpfMaskHandler = (e) => {
              let value = e.target.value.replace(/\D/g, "");
              value = value.replace(/(\d{3})(\d)/, "$1.$2");
              value = value.replace(/(\d{3})(\d)/, "$1.$2");
              value = value.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
              e.target.value = value;
            })
          );
        }

        setupPhoneMask() {
          this.pixKeyInput.addEventListener(
            "input",
            (this.phoneMaskHandler = (e) => {
              let value = e.target.value.replace(/\D/g, "");
              if (value.length <= 10) {
                value = value.replace(/(\d{2})(\d)/, "($1) $2");
                value = value.replace(/(\d{4})(\d)/, "$1-$2");
              } else {
                value = value.replace(/(\d{2})(\d)/, "($1) $2");
                value = value.replace(/(\d{5})(\d)/, "$1-$2");
              }
              e.target.value = value;
            })
          );
        }

        removeMask() {
          if (this.cpfMaskHandler) {
            this.pixKeyInput.removeEventListener("input", this.cpfMaskHandler);
          }
          if (this.phoneMaskHandler) {
            this.pixKeyInput.removeEventListener("input", this.phoneMaskHandler);
          }
        }

        validatePixKey() {
          const keyType = this.keyTypeSelect.value;
          const value = this.pixKeyInput.value.trim();
          const validation = this.keyValidation;

          if (!value) {
            validation.textContent = "";
            validation.className = "validation-message";
            return false;
          }

          let isValid = false;
          let message = "";

          switch (keyType) {
            case "cpf":
              isValid = this.validateCPF(value);
              message = isValid ? "CPF válido" : "CPF inválido";
              break;
            case "phone":
              isValid = this.validatePhone(value);
              message = isValid ? "Telefone válido" : "Telefone inválido";
              break;
            case "email":
              isValid = this.validateEmail(value);
              message = isValid ? "Email válido" : "Email inválido";
              break;
            case "random":
              isValid = value.length >= 10;
              message = isValid ? "Chave válida" : "Chave deve ter pelo menos 10 caracteres";
              break;
          }

          validation.textContent = message;
          validation.className = `validation-message ${isValid ? "success" : "error"}`;

          return isValid;
        }

        validateCPF(cpf) {
          cpf = cpf.replace(/\D/g, "");

          if (cpf.length !== 11) return false;
          if (/^(\d)\1{10}$/.test(cpf)) return false;

          let sum = 0;
          for (let i = 0; i < 9; i++) {
            sum += parseInt(cpf.charAt(i)) * (10 - i);
          }
          let digit1 = ((sum * 10) % 11) % 10;

          if (digit1 !== parseInt(cpf.charAt(9))) return false;

          sum = 0;
          for (let i = 0; i < 10; i++) {
            sum += parseInt(cpf.charAt(i)) * (11 - i);
          }
          let digit2 = ((sum * 10) % 11) % 10;

          return digit2 === parseInt(cpf.charAt(10));
        }

        validatePhone(phone) {
          const digits = phone.replace(/\D/g, "");
          return digits.length === 10 || digits.length === 11;
        }

        validateEmail(email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(email);
        }

        updateCharCounter(input, maxLength) {
          const counter = input.parentElement.querySelector(".char-counter");
          if (counter) {
            counter.textContent = `${input.value.length}/${maxLength} caracteres`;
          }
        }

        async handleFormSubmit(e) {
          e.preventDefault();

          if (!this.validateForm()) {
            return;
          }

          this.setLoading(true);

          try {
            const formData = this.getFormData();
            const brCode = await this.generateBRCode(formData);
            await this.generateAndDisplayQRCode(brCode, formData);

            this.displayResult(brCode, formData);
            this.showToast("QR Code PIX gerado com sucesso!", "success");
          } catch (error) {
            console.error("Error generating QR Code:", error);
            this.showError("Erro ao gerar QR Code: " + error.message);
          } finally {
            this.setLoading(false);
          }
        }

        validateForm() {
          const requiredFields = [
            { element: this.pixKeyInput, name: "Chave PIX" },
            { element: this.receiverNameInput, name: "Nome do recebedor" },
            { element: this.receiverCityInput, name: "Cidade do recebedor" }
          ];

          for (const field of requiredFields) {
            if (!field.element.value.trim()) {
              this.showError(`O campo "${field.name}" é obrigatório.`);
              field.element.focus();
              return false;
            }
          }

          // Validate PIX key
          if (!this.validatePixKey()) {
            this.showError("Por favor, insira uma chave PIX válida.");
            this.pixKeyInput.focus();
            return false;
          }

          return true;
        }

        getFormData() {
          // Parse amount
          let amount = 0;
          if (this.amountInput.value.trim()) {
            const amountStr = this.amountInput.value.replace(/[^\d,]/g, "").replace(",", ".");
            amount = parseFloat(amountStr) || 0;
          }

          // Clean PIX key based on type
          let pixKey = this.pixKeyInput.value.trim();
          const keyType = this.keyTypeSelect.value;

          if (keyType === "cpf") {
            pixKey = pixKey.replace(/\D/g, "");
          } else if (keyType === "phone") {
            pixKey = pixKey.replace(/\D/g, "");
            if (!pixKey.startsWith("55")) {
              pixKey = "55" + pixKey;
            }
            if (!pixKey.startsWith("+")) {
              pixKey = "+" + pixKey;
            }
          }

          return {
            keyType,
            pixKey,
            receiverName: this.receiverNameInput.value.trim(),
            receiverCity: this.receiverCityInput.value.trim(),
            amount,
            reference: this.referenceInput.value.trim(),
            description: this.descriptionInput.value.trim()
          };
        }

        // PIX BR Code generation (simplified version)
        async generateBRCode(data) {
          const getValue = (tag, value) => {
            return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
          };

          const formattedText = (text) => {
            return text
              .normalize("NFD")
              .replace(/[\u0300-\u036f]/g, "")
              .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
          };

          const crcCompute = (data) => {
            // Simplified CRC16 calculation
            let crc = 0xffff;
            const dataBytes = new TextEncoder().encode(data);

            for (const byte of dataBytes) {
              crc ^= byte << 8;
              for (let i = 0; i < 8; i++) {
                if (crc & 0x8000) {
                  crc = (crc << 1) ^ 0x1021;
                } else {
                  crc <<= 1;
                }
              }
            }

            return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
          };

          // Build account information
          const basePix = getValue("00", "br.gov.bcb.pix");
          let infoString = getValue("01", data.pixKey);

          if (data.description) {
            infoString += getValue("02", formattedText(data.description));
          }

          const accountInfo = getValue("26", basePix + infoString);

          // Build additional data field
          const txid = data.reference || "***";
          const additionalData = getValue("62", getValue("05", formattedText(txid)));

          // Build complete BR Code
          let resultString = getValue("00", "01"); // Payload Format Indicator
          resultString += getValue("01", "11"); // Point of Initiation Method (static)
          resultString += accountInfo;
          resultString += getValue("52", "0000"); // Merchant Category Code
          resultString += getValue("53", "986"); // Transaction Currency (BRL)

          if (data.amount > 0) {
            resultString += getValue("54", data.amount.toFixed(2));
          }

          resultString += getValue("58", "BR"); // Country Code
          resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
          resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
          resultString += additionalData;
          resultString += "6304"; // CRC placeholder

          const finalBRCode = resultString + crcCompute(resultString);

          return finalBRCode;
        }

        async generateAndDisplayQRCode(brCode, formData) {
          const customizationActive = this.customizationPanel.classList.contains("active");

          // Sempre usar qr-code-styling para consistência visual
          if (window.QRCodeStyling) {
            if (customizationActive) {
              // Use advanced styling with user customizations
              await this.generateStyledQRCode(brCode);
            } else {
              // Use standardized styling for consistency
              await this.generateStandardQRCode(brCode);
            }
          } else {
            // Fallback apenas se a biblioteca não estiver disponível
            console.warn("QRCodeStyling library not available, falling back to external API");
            await this.generateSimpleQRCode(brCode);
          }
        }

        async generateStyledQRCode(data) {
          const currentMargin = this.getCurrentMargin();

          // If margin is 0, use alternative generation method
          if (currentMargin === 0) {
            console.log("Using alternative QR generation for zero margin");
            return this.generateZeroMarginQR(data);
          }

          // Get user-specified dimensions for export
          const exportWidth = parseInt(document.getElementById("qr-width").value) || 300;
          const exportHeight = parseInt(document.getElementById("qr-height").value) || 300;

          // Create display options with constrained size (max 350px for display)
          const maxDisplaySize = 350;
          const aspectRatio = exportWidth / exportHeight;
          let displayWidth, displayHeight;

          if (exportWidth >= exportHeight) {
            displayWidth = Math.min(exportWidth, maxDisplaySize);
            displayHeight = displayWidth / aspectRatio;
          } else {
            displayHeight = Math.min(exportHeight, maxDisplaySize);
            displayWidth = displayHeight * aspectRatio;
          }

          // Options for display version
          const displayOptions = {
            width: Math.round(displayWidth),
            height: Math.round(displayHeight),
            type: currentMargin <= 1 ? "canvas" : "svg", // Use canvas for very small margins
            data: data,
            margin: currentMargin,
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Only add imageOptions if there's an image
          if (this.currentImageFile) {
            displayOptions.imageOptions = {
              crossOrigin: "anonymous",
              margin: parseInt(document.getElementById("image-margin").value) || 0,
              imageSize: parseFloat(document.getElementById("image-size").value) || 0.4,
              hideBackgroundDots: document.getElementById("hide-background-dots").checked
            };
          }

          // Options for export version (full user-specified size)
          this.exportOptions = {
            width: exportWidth,
            height: exportHeight,
            type: "svg",
            data: data,
            margin: currentMargin,
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Only add imageOptions if there's an image
          if (this.currentImageFile) {
            this.exportOptions.imageOptions = {
              crossOrigin: "anonymous",
              margin: parseInt(document.getElementById("image-margin").value) || 0,
              imageSize: parseFloat(document.getElementById("image-size").value) || 0.4,
              hideBackgroundDots: document.getElementById("hide-background-dots").checked
            };
          }

          // Add image if present
          if (this.currentImageFile) {
            const reader = new FileReader();
            return new Promise((resolve) => {
              reader.onload = (e) => {
                displayOptions.image = e.target.result;
                this.exportOptions.image = e.target.result;

                // Add imageOptions for images
                if (!displayOptions.imageOptions) {
                  displayOptions.imageOptions = {
                    crossOrigin: "anonymous",
                    margin: parseInt(document.getElementById("image-margin").value) || 0,
                    imageSize: parseFloat(document.getElementById("image-size").value) || 0.4,
                    hideBackgroundDots: document.getElementById("hide-background-dots").checked
                  };
                }
                if (!this.exportOptions.imageOptions) {
                  this.exportOptions.imageOptions = {
                    crossOrigin: "anonymous",
                    margin: parseInt(document.getElementById("image-margin").value) || 0,
                    imageSize: parseFloat(document.getElementById("image-size").value) || 0.4,
                    hideBackgroundDots: document.getElementById("hide-background-dots").checked
                  };
                }

                this.createQRCode(displayOptions);
                resolve();
              };
              reader.readAsDataURL(this.currentImageFile);
            });
          } else {
            this.createQRCode(displayOptions);
          }
        }

        createQRCode(options) {
          console.log("createQRCode called with options:", options);
          console.log("Margin value being used:", options.margin);

          this.qrCode = new window.QRCodeStyling(options);

          // Clear preview
          this.qrPreview.innerHTML = "";

          // Add QR Code
          this.qrCode.append(this.qrPreview);

          // Post-process for zero margin if needed
          if (options.margin === 0) {
            setTimeout(() => this.forceZeroMargin(), 100);
          }

          console.log("QR Code created and appended");
        }

        forceZeroMargin() {
          console.log("forceZeroMargin called");

          // Find the SVG or Canvas element
          const svg = this.qrPreview.querySelector("svg");
          const canvas = this.qrPreview.querySelector("canvas");

          if (svg) {
            console.log("Found SVG, manipulating viewBox and removing margin");
            // Remove any margin/padding from SVG
            svg.style.margin = "0";
            svg.style.padding = "0";

            // Try to adjust viewBox to eliminate internal margin
            const viewBox = svg.getAttribute("viewBox");
            if (viewBox) {
              console.log("Original viewBox:", viewBox);
              const parts = viewBox.split(" ");
              if (parts.length === 4) {
                // Expand the viewBox to crop out margins
                const margin = 5; // Estimated internal margin
                parts[0] = (parseFloat(parts[0]) + margin).toString();
                parts[1] = (parseFloat(parts[1]) + margin).toString();
                parts[2] = (parseFloat(parts[2]) - margin * 2).toString();
                parts[3] = (parseFloat(parts[3]) - margin * 2).toString();
                const newViewBox = parts.join(" ");
                svg.setAttribute("viewBox", newViewBox);
                console.log("New viewBox:", newViewBox);
              }
            }
          }

          if (canvas) {
            console.log("Found Canvas, manipulating margins");
            canvas.style.margin = "0";
            canvas.style.padding = "0";
          }
        }

        async generateZeroMarginQR(data) {
          console.log("generateZeroMarginQR called - using optimized QR generation");

          try {
            // OPTIMIZATION: Try hybrid approach first - use margin=1 with CSS cropping for better quality
            const useHybridApproach = this.shouldUseHybridApproach();

            if (useHybridApproach) {
              console.log("Using hybrid approach (margin=1 + CSS cropping)");
              await this.generateHybridZeroMarginQR(data);
            } else {
              console.log("Using native zero margin processing");
              // Use native QR generation with zero margin
              const displayCanvas = await this.createNativeZeroMarginQR(data);

              // Clear preview and add the zero-margin QR code
              this.qrPreview.innerHTML = "";
              this.qrPreview.appendChild(displayCanvas);

              // Store canvases for download (export canvas is created in processQRWithAllOptions)
              this.qrCode = null; // Clear the QRCodeStyling instance
              // zeroMarginExportCanvas is already stored in processQRWithAllOptions
              this.zeroMarginCanvas = this.zeroMarginExportCanvas; // Use export canvas for download

              console.log("Native zero margin QR code generated successfully");
              console.log(
                "Export canvas dimensions:",
                this.zeroMarginExportCanvas
                  ? `${this.zeroMarginExportCanvas.width}x${this.zeroMarginExportCanvas.height}`
                  : "not found"
              );
            }
          } catch (error) {
            console.error("Zero margin QR generation error:", error);
            // Fallback to styled QR with margin 1
            console.log("Falling back to margin 1");
            const fallbackData = data;
            this.generateStyledQRCodeWithFallback(fallbackData, 1);
          }
        }

        async createNativeZeroMarginQR(data) {
          // Get user-specified dimensions for export
          const exportWidth = parseInt(document.getElementById("qr-width").value) || 300;
          const exportHeight = parseInt(document.getElementById("qr-height").value) || 300;

          // Create display options with constrained size (max 350px for display)
          const maxDisplaySize = 350;
          const aspectRatio = exportWidth / exportHeight;
          let displayWidth, displayHeight;

          if (exportWidth >= exportHeight) {
            displayWidth = Math.min(exportWidth, maxDisplaySize);
            displayHeight = displayWidth / aspectRatio;
          } else {
            displayHeight = Math.min(exportHeight, maxDisplaySize);
            displayWidth = displayHeight * aspectRatio;
          }

          // Build display options (for preview - limited size)
          const displayOptions = {
            width: Math.round(displayWidth),
            height: Math.round(displayHeight),
            type: "canvas",
            data: data,
            margin: 0,
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Build export options (for download - full user-specified size)
          const exportOptions = {
            width: exportWidth,
            height: exportHeight,
            type: "canvas",
            data: data,
            margin: 0,
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Add imageOptions to both display and export options if there's an image
          if (this.currentImageFile) {
            const imageOptions = {
              crossOrigin: "anonymous",
              margin: parseInt(document.getElementById("image-margin").value) || 0,
              imageSize: parseFloat(document.getElementById("image-size").value) || 0.4,
              hideBackgroundDots: document.getElementById("hide-background-dots").checked
            };

            displayOptions.imageOptions = imageOptions;
            exportOptions.imageOptions = imageOptions;

            // Read the image file and add it to options
            const reader = new FileReader();
            return new Promise((resolve, reject) => {
              reader.onload = async (e) => {
                try {
                  displayOptions.image = e.target.result;
                  exportOptions.image = e.target.result;

                  const result = await this.processQRWithAllOptions(displayOptions, exportOptions);
                  resolve(result);
                } catch (error) {
                  reject(error);
                }
              };
              reader.onerror = () => reject(new Error("Failed to read image file"));
              reader.readAsDataURL(this.currentImageFile);
            });
          } else {
            // No image, process directly
            return this.processQRWithAllOptions(displayOptions, exportOptions);
          }
        }

        async processQRWithAllOptions(displayOptions, exportOptions) {
          // Create display canvas (for preview - limited size)
          const displayCanvas = await this.createZeroMarginCanvas(displayOptions);

          // Create export canvas (for download - full user-specified size)
          const exportCanvas = await this.createZeroMarginCanvas(exportOptions);

          // Store both canvases
          this.zeroMarginDisplayCanvas = displayCanvas;
          this.zeroMarginExportCanvas = exportCanvas;

          // Return display canvas for preview
          return displayCanvas;
        }

        async createZeroMarginCanvas(options) {
          // Create a temporary QRCodeStyling instance with all personalizations
          const tempQR = new window.QRCodeStyling(options);

          // Create a temporary container to render the QR code
          const tempContainer = document.createElement("div");
          tempContainer.style.position = "absolute";
          tempContainer.style.left = "-9999px";
          tempContainer.style.top = "-9999px";
          document.body.appendChild(tempContainer);

          // Render the QR code
          tempQR.append(tempContainer);

          // Wait for rendering to complete
          await new Promise((resolve) => setTimeout(resolve, 150));

          // Get the canvas from the temporary container
          const tempCanvas = tempContainer.querySelector("canvas");
          if (!tempCanvas) {
            document.body.removeChild(tempContainer);
            throw new Error("Failed to generate QR code canvas");
          }

          // Create our final canvas with exact QR dimensions (no margin)
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");

          // OPTIMIZATION: Disable image smoothing for crisp pixels
          ctx.imageSmoothingEnabled = false;
          ctx.mozImageSmoothingEnabled = false;
          ctx.webkitImageSmoothingEnabled = false;
          ctx.msImageSmoothingEnabled = false;

          // Calculate the actual QR code area (removing any internal margin)
          const sourceCanvas = tempCanvas;
          const sourceCtx = sourceCanvas.getContext("2d");
          const imageData = sourceCtx.getImageData(0, 0, sourceCanvas.width, sourceCanvas.height);

          // Find the actual QR code boundaries by scanning for non-background pixels
          const backgroundColor = document.getElementById("background-color").value || "#FFFFFF";
          const bgColor = this.hexToRgb(backgroundColor);

          let minX = sourceCanvas.width,
            minY = sourceCanvas.height;
          let maxX = 0,
            maxY = 0;

          // IMPROVEMENT: Better edge detection with higher tolerance and smarter algorithm
          const tolerance = 30; // Increased from 10 to 30 for better edge detection

          for (let y = 0; y < sourceCanvas.height; y++) {
            for (let x = 0; x < sourceCanvas.width; x++) {
              const idx = (y * sourceCanvas.width + x) * 4;
              const r = imageData.data[idx];
              const g = imageData.data[idx + 1];
              const b = imageData.data[idx + 2];
              const a = imageData.data[idx + 3];

              // Enhanced pixel detection: check alpha channel and use better color distance
              const colorDistance = Math.sqrt(
                Math.pow(r - bgColor.r, 2) + Math.pow(g - bgColor.g, 2) + Math.pow(b - bgColor.b, 2)
              );

              // Check if pixel is not background color (improved detection)
              if (a > 128 && colorDistance > tolerance) {
                minX = Math.min(minX, x);
                minY = Math.min(minY, y);
                maxX = Math.max(maxX, x);
                maxY = Math.max(maxY, y);
              }
            }
          }

          // SAFETY: Ensure we found valid boundaries
          if (minX >= maxX || minY >= maxY) {
            console.warn("Could not detect QR code boundaries, using full canvas");
            minX = 0;
            minY = 0;
            maxX = sourceCanvas.width - 1;
            maxY = sourceCanvas.height - 1;
          }

          // Set canvas size to the actual QR code dimensions
          const qrWidth = maxX - minX + 1;
          const qrHeight = maxY - minY + 1;

          // IMPROVEMENT: Use pixel-perfect scaling when possible
          const targetWidth = options.width;
          const targetHeight = options.height;

          // Calculate optimal scale to maintain crisp pixels
          const scaleX = targetWidth / qrWidth;
          const scaleY = targetHeight / qrHeight;
          const scale = Math.min(scaleX, scaleY);

          // Use integer scaling when possible for maximum sharpness
          const integerScale = Math.floor(scale);
          const useIntegerScale = integerScale >= 1 && scale - integerScale < 0.1;
          const finalScale = useIntegerScale ? integerScale : scale;

          canvas.width = targetWidth;
          canvas.height = targetHeight;

          // Center the QR code in the canvas
          const scaledWidth = qrWidth * finalScale;
          const scaledHeight = qrHeight * finalScale;
          const offsetX = Math.round((targetWidth - scaledWidth) / 2);
          const offsetY = Math.round((targetHeight - scaledHeight) / 2);

          // Fill background with exact color
          ctx.fillStyle = backgroundColor;
          ctx.fillRect(0, 0, targetWidth, targetHeight);

          // OPTIMIZATION: Use pixel-perfect rendering
          if (useIntegerScale && integerScale >= 2) {
            // For integer scales >= 2, use pixel-by-pixel rendering for maximum sharpness
            this.renderPixelPerfect(
              ctx,
              sourceCtx,
              imageData,
              minX,
              minY,
              qrWidth,
              qrHeight,
              offsetX,
              offsetY,
              integerScale,
              bgColor,
              tolerance
            );
          } else {
            // For non-integer scales, use optimized drawImage
            ctx.save();
            ctx.imageSmoothingEnabled = false;
            ctx.drawImage(sourceCanvas, minX, minY, qrWidth, qrHeight, offsetX, offsetY, scaledWidth, scaledHeight);
            ctx.restore();
          }

          // Set display styles only for display canvas
          if (options.width <= 350) {
            canvas.style.maxWidth = "100%";
            canvas.style.borderRadius = "8px";
            canvas.style.margin = "0";
            canvas.style.padding = "0";
            canvas.style.imageRendering = "pixelated"; // CSS hint for crisp rendering
            canvas.style.imageRendering = "-moz-crisp-edges";
            canvas.style.imageRendering = "crisp-edges";
          }

          // Clean up
          document.body.removeChild(tempContainer);

          return canvas;
        }

        renderPixelPerfect(
          ctx,
          sourceCtx,
          imageData,
          minX,
          minY,
          qrWidth,
          qrHeight,
          offsetX,
          offsetY,
          scale,
          bgColor,
          tolerance
        ) {
          // Pixel-perfect rendering for integer scales >= 2
          // This method renders each source pixel as a perfect square block

          console.log("Using pixel-perfect rendering with scale:", scale);

          for (let y = 0; y < qrHeight; y++) {
            for (let x = 0; x < qrWidth; x++) {
              const sourceX = minX + x;
              const sourceY = minY + y;
              const idx = (sourceY * imageData.width + sourceX) * 4;

              const r = imageData.data[idx];
              const g = imageData.data[idx + 1];
              const b = imageData.data[idx + 2];
              const a = imageData.data[idx + 3];

              // Enhanced pixel detection using the same algorithm as edge detection
              const colorDistance = Math.sqrt(
                Math.pow(r - bgColor.r, 2) + Math.pow(g - bgColor.g, 2) + Math.pow(b - bgColor.b, 2)
              );

              // Determine if this pixel should be rendered as foreground
              const isForeground = a > 128 && colorDistance > tolerance;

              if (isForeground) {
                // Render as a perfect square block
                const targetX = offsetX + x * scale;
                const targetY = offsetY + y * scale;

                ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${a / 255})`;
                ctx.fillRect(Math.round(targetX), Math.round(targetY), scale, scale);
              }
            }
          }
        }

        shouldUseHybridApproach() {
          // Use hybrid approach when:
          // 1. No custom image is being used (simpler case)
          // 2. Standard dot types are being used
          // 3. Target size is reasonable for CSS cropping

          const hasCustomImage = this.currentImageFile !== null;
          const dotType = document.getElementById("dots-type").value;
          const targetWidth = parseInt(document.getElementById("qr-width").value) || 300;

          const isStandardDotType = ["square", "rounded", "dots"].includes(dotType);
          const isReasonableSize = targetWidth >= 200 && targetWidth <= 600;

          return !hasCustomImage && isStandardDotType && isReasonableSize;
        }

        async generateHybridZeroMarginQR(data) {
          // Hybrid approach: Generate with margin=1, then use CSS to crop visually
          console.log("Generating hybrid zero-margin QR code");

          // Get user-specified dimensions for export
          const exportWidth = parseInt(document.getElementById("qr-width").value) || 300;
          const exportHeight = parseInt(document.getElementById("qr-height").value) || 300;

          // Create display options with margin=1 (minimal margin for better quality)
          const maxDisplaySize = 350;
          const aspectRatio = exportWidth / exportHeight;
          let displayWidth, displayHeight;

          if (exportWidth >= exportHeight) {
            displayWidth = Math.min(exportWidth, maxDisplaySize);
            displayHeight = displayWidth / aspectRatio;
          } else {
            displayHeight = Math.min(exportHeight, maxDisplaySize);
            displayWidth = displayHeight * aspectRatio;
          }

          const displayOptions = {
            width: Math.round(displayWidth),
            height: Math.round(displayHeight),
            type: "canvas",
            data: data,
            margin: 1, // Use minimal margin instead of 0
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Create QR code with margin=1
          this.qrCode = new window.QRCodeStyling(displayOptions);

          // Clear preview
          this.qrPreview.innerHTML = "";

          // Add QR Code
          this.qrCode.append(this.qrPreview);

          // Apply CSS cropping to simulate zero margin
          const canvas = this.qrPreview.querySelector("canvas");
          if (canvas) {
            // Apply CSS transforms to crop the margin visually
            canvas.style.transform = "scale(1.05)"; // Slightly scale up to crop margin
            canvas.style.transformOrigin = "center center";
            canvas.style.overflow = "hidden";

            // Ensure crisp rendering
            canvas.style.imageRendering = "pixelated";
            canvas.style.imageRendering = "-moz-crisp-edges";
            canvas.style.imageRendering = "crisp-edges";
          }

          // Store for download - create export version
          this.zeroMarginCanvas = await this.createHybridExportCanvas(data, exportWidth, exportHeight);

          console.log("Hybrid zero-margin QR code generated successfully");
        }

        async createHybridExportCanvas(data, width, height) {
          // Create export version with margin=1 and then crop it properly
          const exportOptions = {
            width: width,
            height: height,
            type: "canvas",
            data: data,
            margin: 1, // Use minimal margin
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          // Create temporary QR code for export
          const tempQR = new window.QRCodeStyling(exportOptions);

          // Create temporary container
          const tempContainer = document.createElement("div");
          tempContainer.style.position = "absolute";
          tempContainer.style.left = "-9999px";
          tempContainer.style.top = "-9999px";
          document.body.appendChild(tempContainer);

          // Render the QR code
          tempQR.append(tempContainer);
          await new Promise((resolve) => setTimeout(resolve, 100));

          const tempCanvas = tempContainer.querySelector("canvas");
          if (!tempCanvas) {
            document.body.removeChild(tempContainer);
            throw new Error("Failed to generate export canvas");
          }

          // Create final export canvas with proper cropping
          const exportCanvas = document.createElement("canvas");
          const ctx = exportCanvas.getContext("2d");

          // Disable smoothing for crisp pixels
          ctx.imageSmoothingEnabled = false;

          exportCanvas.width = width;
          exportCanvas.height = height;

          // Calculate crop area (remove 1px margin on all sides)
          const cropMargin = Math.max(1, Math.round(width * 0.003)); // Adaptive margin based on size
          const sourceX = cropMargin;
          const sourceY = cropMargin;
          const sourceWidth = tempCanvas.width - 2 * cropMargin;
          const sourceHeight = tempCanvas.height - 2 * cropMargin;

          // Draw cropped version
          ctx.drawImage(tempCanvas, sourceX, sourceY, sourceWidth, sourceHeight, 0, 0, width, height);

          // Clean up
          document.body.removeChild(tempContainer);

          return exportCanvas;
        }

        hexToRgb(hex) {
          const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
          return result
            ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
              }
            : { r: 255, g: 255, b: 255 };
        }

        async generateStyledQRCodeWithFallback(data, forcedMargin) {
          // This is the original method but with a forced margin
          const exportWidth = parseInt(document.getElementById("qr-width").value) || 300;
          const exportHeight = parseInt(document.getElementById("qr-height").value) || 300;

          const maxDisplaySize = 350;
          const aspectRatio = exportWidth / exportHeight;
          let displayWidth, displayHeight;

          if (exportWidth >= exportHeight) {
            displayWidth = Math.min(exportWidth, maxDisplaySize);
            displayHeight = displayWidth / aspectRatio;
          } else {
            displayHeight = Math.min(exportHeight, maxDisplaySize);
            displayWidth = displayHeight * aspectRatio;
          }

          const displayOptions = {
            width: Math.round(displayWidth),
            height: Math.round(displayHeight),
            type: "svg",
            data: data,
            margin: forcedMargin,
            dotsOptions: {
              color: document.getElementById("dots-color").value,
              type: document.getElementById("dots-type").value
            },
            backgroundOptions: {
              color: document.getElementById("background-color").value
            },
            cornersSquareOptions: {
              color: document.getElementById("corner-square-color").value,
              type: document.getElementById("corner-square-type").value || undefined
            },
            cornersDotOptions: {
              color: document.getElementById("corner-dot-color").value,
              type: document.getElementById("corner-dot-type").value || undefined
            }
          };

          this.createQRCode(displayOptions);
        }

        async generateStandardQRCode(brCode) {
          try {
            console.log("Generating standard QR code with qr-code-styling library");

            // Configurações padronizadas para consistência visual
            const standardOptions = {
              width: 300,
              height: 300,
              type: "svg",
              data: brCode,
              margin: 10, // Margem padrão consistente
              qrOptions: {
                typeNumber: 0, // Auto-detect
                mode: undefined,
                errorCorrectionLevel: "M" // Nível M para consistência com APIs externas
              },
              imageOptions: {
                saveAsBlob: true,
                hideBackgroundDots: true,
                imageSize: 0.4,
                crossOrigin: undefined,
                margin: 0
              },
              dotsOptions: {
                type: "square", // Tipo padrão simples
                color: "#000000",
                roundSize: true
              },
              backgroundOptions: {
                round: 0,
                color: "#ffffff"
              },
              cornersSquareOptions: {
                type: undefined, // Usar padrão da biblioteca
                color: "#000000"
              },
              cornersDotOptions: {
                type: undefined, // Usar padrão da biblioteca
                color: "#000000"
              }
            };

            // Criar QR Code com configurações padronizadas
            this.qrCode = new window.QRCodeStyling(standardOptions);

            // Armazenar opções de exportação (mesmo que as de display para simplicidade)
            this.exportOptions = { ...standardOptions };

            // Limpar preview e adicionar QR Code
            this.qrPreview.innerHTML = "";
            this.qrCode.append(this.qrPreview);

            console.log("Standard QR code generated successfully");
          } catch (error) {
            console.error("Standard QR Code generation error:", error);
            throw new Error("Não foi possível gerar o QR Code. Tente novamente.");
          }
        }

        async generateSimpleQRCode(brCode) {
          try {
            console.warn("Using fallback external API for QR code generation");
            // Using QR Server API as fallback with standardized error correction
            const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&ecc=M&data=${encodeURIComponent(brCode)}`;

            const response = await fetch(qrApiUrl);
            if (!response.ok) {
              throw new Error("Failed to generate QR code");
            }

            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);

            this.qrPreview.innerHTML = `<img src="${imageUrl}" alt="QR Code PIX" style="max-width: 100%; border-radius: 8px;">`;
          } catch (error) {
            console.error("QR Code generation error:", error);
            throw new Error("Não foi possível gerar o QR Code. Tente novamente.");
          }
        }

        generateQRIfReady() {
          // Only regenerate if QR is already displayed and form is valid
          if (this.qrResult.style.display !== "none" && this.currentBRCode) {
            const customizationActive = this.customizationPanel.classList.contains("active");

            // Clear any existing QR code instances to prevent conflicts with new optimizations
            this.qrCode = null;
            this.zeroMarginCanvas = null;
            this.zeroMarginExportCanvas = null;
            this.zeroMarginDisplayCanvas = null;

            // Sempre usar qr-code-styling para consistência visual
            if (window.QRCodeStyling) {
              if (customizationActive) {
                // Use styled QR code generation with user customizations
                this.generateStyledQRCode(this.currentBRCode);
              } else {
                // Use standardized QR code generation
                this.generateStandardQRCode(this.currentBRCode);
              }
            } else {
              // Fallback apenas se a biblioteca não estiver disponível
              console.warn("QRCodeStyling library not available, falling back to external API");
              this.generateSimpleQRCode(this.currentBRCode);
            }
          }
        }

        displayResult(brCode, formData) {
          this.currentBRCode = brCode;

          // Hide placeholder and show result
          this.qrPlaceholder.style.display = "none";
          this.qrResult.style.display = "block";

          // Set BR Code text
          this.brCodeText.textContent = brCode;

          // Display PIX details
          this.displayPixDetails(formData);

          // Scroll to result
          this.qrResult.scrollIntoView({ behavior: "smooth", block: "center" });
        }

        displayPixDetails(data) {
          const details = [
            { label: "Recebedor", value: data.receiverName },
            { label: "Cidade", value: data.receiverCity },
            { label: "Chave PIX", value: data.pixKey },
            {
              label: "Valor",
              value: data.amount > 0 ? `R$ ${data.amount.toFixed(2).replace(".", ",")}` : "Valor livre"
            }
          ];

          if (data.reference) {
            details.push({ label: "Referência", value: data.reference });
          }

          if (data.description) {
            details.push({ label: "Descrição", value: data.description });
          }

          const detailsHTML = details
            .map(
              (item) => `
                    <div class="pix-detail-item">
                        <span class="pix-detail-label">${item.label}:</span>
                        <span class="pix-detail-value">${item.value}</span>
                    </div>
                `
            )
            .join("");

          this.pixDetails.innerHTML = detailsHTML;
        }

        downloadQRCode(format) {
          try {
            console.log("Download requested for format:", format);

            // Handle zero margin download with native canvas (only for customized QR codes)
            if (this.zeroMarginCanvas && this.getCurrentMargin() === 0) {
              const link = document.createElement("a");
              link.download = `qrcode-pix-zero-margin.${format === "svg" ? "png" : format}`;

              // Convert canvas to data URL
              const dataURL = this.zeroMarginCanvas.toDataURL(`image/${format === "svg" ? "png" : format}`);
              link.href = dataURL;
              link.click();
              this.showToast(`QR Code baixado como ${format.toUpperCase()}!`, "success");
              return;
            }

            // Handle QR code download using qr-code-styling library
            if (this.qrCode && typeof window.QRCodeStyling !== "undefined") {
              let filename = "qrcode-pix";

              // Use export options if available (for customized QR codes)
              if (this.exportOptions) {
                // Create export version with user-specified dimensions
                const exportQR = new window.QRCodeStyling({
                  ...this.exportOptions,
                  type: format === "svg" ? "svg" : "canvas"
                });

                filename = "qrcode-pix-personalizado";
                exportQR.download({
                  name: filename,
                  extension: format
                });
              } else {
                // Use standard QR code for download (both standard and customized without export options)
                filename = "qrcode-pix-padrao";
                this.qrCode.download({
                  name: filename,
                  extension: format
                });
              }
            } else {
              // Fallback: Handle simple QR code (image) download from external API
              const img = this.qrPreview.querySelector("img");
              if (img) {
                const link = document.createElement("a");
                link.download = `qrcode-pix-simples.${format === "svg" ? "png" : format}`;
                link.href = img.src;
                link.click();
              } else {
                throw new Error("Nenhum QR Code encontrado para download");
              }
            }

            this.showToast(`QR Code baixado como ${format.toUpperCase()}!`, "success");
          } catch (error) {
            console.error("Download error:", error);
            this.showError("Erro ao fazer download do QR Code.");
          }
        }

        async copyBRCode() {
          if (!this.currentBRCode) return;

          try {
            await navigator.clipboard.writeText(this.currentBRCode);
            this.showToast("BR Code copiado para a área de transferência!", "success");
          } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = this.currentBRCode;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);

            this.showToast("BR Code copiado!", "success");
          }
        }

        setLoading(loading) {
          this.generateBtn.disabled = loading;
          this.generateBtn.classList.toggle("loading", loading);

          if (loading) {
            this.loadingSpinner.style.display = "block";
          } else {
            this.loadingSpinner.style.display = "none";
          }
        }

        showError(message) {
          this.errorMessage.textContent = message;
          this.errorModal.style.display = "block";
        }

        hideModal() {
          this.errorModal.style.display = "none";
        }

        showToast(message, type = "success") {
          const toast = document.createElement("div");
          toast.className = `toast ${type}`;
          toast.textContent = message;

          document.body.appendChild(toast);

          // Show toast
          setTimeout(() => toast.classList.add("show"), 100);

          // Hide toast
          setTimeout(() => {
            toast.classList.remove("show");
            setTimeout(() => document.body.removeChild(toast), 300);
          }, 3000);
        }
      }

      // Preset functions (from original qr-generator.html)
      function applyPreset(presetName) {
        const presets = {
          modern: {
            dotsType: "rounded",
            dotsColor: "#667eea",
            cornerSquareType: "extra-rounded",
            cornerSquareColor: "#764ba2",
            cornerDotType: "rounded",
            cornerDotColor: "#667eea",
            backgroundColor: "#ffffff"
          },
          classic: {
            dotsType: "square",
            dotsColor: "#000000",
            cornerSquareType: "square",
            cornerSquareColor: "#000000",
            cornerDotType: "square",
            cornerDotColor: "#000000",
            backgroundColor: "#ffffff"
          },
          elegant: {
            dotsType: "classy-rounded",
            dotsColor: "#2c3e50",
            cornerSquareType: "rounded",
            cornerSquareColor: "#34495e",
            cornerDotType: "rounded",
            cornerDotColor: "#2c3e50",
            backgroundColor: "#ecf0f1"
          },
          vibrant: {
            dotsType: "dots",
            dotsColor: "#e74c3c",
            cornerSquareType: "extra-rounded",
            cornerSquareColor: "#3498db",
            cornerDotType: "dot",
            cornerDotColor: "#f39c12",
            backgroundColor: "#ffffff"
          },
          circular: {
            dotsType: "rounded",
            dotsColor: "#2c3e50",
            cornerSquareType: "dot",
            cornerSquareColor: "#e74c3c",
            cornerDotType: "dot",
            cornerDotColor: "#3498db",
            backgroundColor: "#ffffff"
          }
        };

        const preset = presets[presetName];
        if (preset) {
          document.getElementById("dots-type").value = preset.dotsType;
          document.getElementById("dots-color").value = preset.dotsColor;
          document.getElementById("corner-square-type").value = preset.cornerSquareType;
          document.getElementById("corner-square-color").value = preset.cornerSquareColor;
          document.getElementById("corner-dot-type").value = preset.cornerDotType;
          document.getElementById("corner-dot-color").value = preset.cornerDotColor;
          document.getElementById("background-color").value = preset.backgroundColor;

          // Regenerate QR if already displayed
          if (window.pixGenerator && window.pixGenerator.currentBRCode) {
            window.pixGenerator.generateQRIfReady();
          }
        }
      }

      // Initialize the application when DOM is ready
      document.addEventListener("DOMContentLoaded", () => {
        window.pixGenerator = new PixQRGeneratorIntegrated();
      });
    </script>
  </body>
</html>
