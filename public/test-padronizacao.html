<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Padronização QR Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        .qr-section {
            border: 2px solid #ddd;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .qr-section h3 {
            margin-top: 0;
            color: #333;
        }
        .qr-preview {
            min-height: 320px;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .comparison {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🔍 Teste de Padronização QR Code</h1>
    
    <div class="info">
        <strong>Objetivo:</strong> Verificar se os QR Codes gerados com e sem personalização agora têm a mesma densidade visual.
        <br><br>
        <strong>Mudanças implementadas:</strong>
        <ul>
            <li>Sempre usa biblioteca qr-code-styling quando disponível</li>
            <li>Configurações padronizadas para QR Codes sem personalização</li>
            <li>Nível de correção de erro padronizado (M) para consistência</li>
        </ul>
    </div>

    <div class="container">
        <div class="qr-section">
            <h3>📱 QR Code Padrão (Sem Personalização)</h3>
            <div class="qr-preview" id="qr-standard"></div>
            <button onclick="generateStandardQR()">Gerar QR Padrão</button>
            <button onclick="downloadQR('standard', 'png')">Download PNG</button>
        </div>

        <div class="qr-section">
            <h3>🎨 QR Code Personalizado (Com Personalização)</h3>
            <div class="qr-preview" id="qr-custom"></div>
            <button onclick="generateCustomQR()">Gerar QR Personalizado</button>
            <button onclick="downloadQR('custom', 'png')">Download PNG</button>
        </div>
    </div>

    <div class="comparison">
        <h3>📊 Análise Visual</h3>
        <p>Compare os dois QR Codes acima. Eles devem ter:</p>
        <ul>
            <li>✅ Mesma densidade de pontos</li>
            <li>✅ Mesmo nível de correção de erro (M)</li>
            <li>✅ Mesma versão/tipo de QR Code</li>
            <li>✅ Aparência visual consistente</li>
        </ul>
    </div>

    <script src="../lib/qr-code-styling.js"></script>
    <script>
        // Dados de teste PIX
        const testBRCode = "00020126580014br.gov.bcb.pix0136123e4567-e12b-12d1-a456-426614174000520400005303986540510.005802BR5913Fulano de Tal6008BRASILIA62070503***6304A1B2";
        
        let standardQR = null;
        let customQR = null;

        // Configurações padronizadas (mesmas do arquivo principal)
        const standardOptions = {
            width: 300,
            height: 300,
            type: "svg",
            data: testBRCode,
            margin: 10,
            qrOptions: {
                typeNumber: 0, // Auto-detect
                mode: undefined,
                errorCorrectionLevel: "M" // Nível M para consistência
            },
            imageOptions: {
                saveAsBlob: true,
                hideBackgroundDots: true,
                imageSize: 0.4,
                crossOrigin: undefined,
                margin: 0
            },
            dotsOptions: {
                type: "square",
                color: "#000000",
                roundSize: true
            },
            backgroundOptions: {
                round: 0,
                color: "#ffffff"
            },
            cornersSquareOptions: {
                type: undefined,
                color: "#000000"
            },
            cornersDotOptions: {
                type: undefined,
                color: "#000000"
            }
        };

        // Configurações personalizadas (com mesmo nível de correção)
        const customOptions = {
            width: 300,
            height: 300,
            type: "svg",
            data: testBRCode,
            margin: 10,
            qrOptions: {
                typeNumber: 0, // Auto-detect
                mode: undefined,
                errorCorrectionLevel: "M" // Mesmo nível M
            },
            imageOptions: {
                saveAsBlob: true,
                hideBackgroundDots: true,
                imageSize: 0.4,
                crossOrigin: undefined,
                margin: 0
            },
            dotsOptions: {
                type: "rounded", // Personalização: pontos arredondados
                color: "#2563eb", // Personalização: cor azul
                roundSize: true
            },
            backgroundOptions: {
                round: 0,
                color: "#ffffff"
            },
            cornersSquareOptions: {
                type: "extra-rounded", // Personalização: cantos arredondados
                color: "#1d4ed8"
            },
            cornersDotOptions: {
                type: "dot", // Personalização: pontos centrais circulares
                color: "#1e40af"
            }
        };

        function generateStandardQR() {
            const container = document.getElementById('qr-standard');
            container.innerHTML = '';
            
            standardQR = new QRCodeStyling(standardOptions);
            standardQR.append(container);
            
            console.log('QR Code Padrão gerado com configurações:', standardOptions);
        }

        function generateCustomQR() {
            const container = document.getElementById('qr-custom');
            container.innerHTML = '';
            
            customQR = new QRCodeStyling(customOptions);
            customQR.append(container);
            
            console.log('QR Code Personalizado gerado com configurações:', customOptions);
        }

        function downloadQR(type, format) {
            const qr = type === 'standard' ? standardQR : customQR;
            if (qr) {
                qr.download({
                    name: `qr-${type}-test`,
                    extension: format
                });
            } else {
                alert(`Gere o QR Code ${type} primeiro!`);
            }
        }

        // Gerar ambos os QR Codes automaticamente ao carregar a página
        window.addEventListener('load', () => {
            setTimeout(() => {
                generateStandardQR();
                generateCustomQR();
            }, 500);
        });
    </script>
</body>
</html>
